#!/usr/bin/env node

/**
 * Verification script to test the @event-services/netsuite-http package
 */

const path = require('path');

console.log('🔍 Verifying @event-services/netsuite-http package...\n');

try {
  // Test importing the package
  console.log('1. Testing package import...');
  const netsuiteHttp = require('@event-services/netsuite-http');
  
  // Check if main exports are available
  const expectedExports = [
    'NetSuiteHttpClient',
    'makeNetSuiteRequest',
    'buildNetSuiteUrl',
    'handleApiError',
    'setOptimizedHeaders',
    'addPerformanceHeaders',
    'validateEnvironment',
    'createConfigFromEnv',
    'getDefaultClient'
  ];
  
  console.log('2. Checking exports...');
  const missingExports = expectedExports.filter(exportName => !netsuiteHttp[exportName]);
  
  if (missingExports.length > 0) {
    console.error(`❌ Missing exports: ${missingExports.join(', ')}`);
    process.exit(1);
  }
  
  console.log('✅ All expected exports are available');
  
  // Test buildNetSuiteUrl function with a client instance
  console.log('3. Testing buildNetSuiteUrl function...');
  try {
    const testConfig = {
      accountId: 'TEST_ACCOUNT',
      accountUrlId: 'test_account',
      oauth1: {
        consumerKey: 'test_key',
        consumerSecret: 'test_secret',
        accessToken: 'test_token',
        accessTokenSecret: 'test_token_secret'
      }
    };

    const client = new netsuiteHttp.NetSuiteHttpClient(testConfig);
    const testUrl = client.buildUrl('test_script', 'test_deploy', { param1: 'value1' });

    if (testUrl.includes('test_script') && testUrl.includes('test_deploy') && testUrl.includes('param1=value1')) {
      console.log('✅ buildNetSuiteUrl function works correctly');
    } else {
      console.error('❌ buildNetSuiteUrl function returned unexpected result');
      console.error('   Expected URL to contain: test_script, test_deploy, param1=value1');
      console.error('   Actual URL:', testUrl);
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error testing buildNetSuiteUrl:', error.message);
    process.exit(1);
  }
  
  // Test NetSuiteHttpClient class
  console.log('4. Testing NetSuiteHttpClient class...');
  try {
    const testConfig = {
      accountId: 'TEST_ACCOUNT',
      accountUrlId: 'test_account',
      oauth1: {
        consumerKey: 'test_key',
        consumerSecret: 'test_secret',
        accessToken: 'test_token',
        accessTokenSecret: 'test_token_secret'
      }
    };
    
    const client = new netsuiteHttp.NetSuiteHttpClient(testConfig);
    if (client && typeof client.buildUrl === 'function' && typeof client.request === 'function') {
      console.log('✅ NetSuiteHttpClient class instantiates correctly');
    } else {
      console.error('❌ NetSuiteHttpClient class missing expected methods');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error testing NetSuiteHttpClient:', error.message);
    process.exit(1);
  }
  
  console.log('\n🎉 Package verification completed successfully!');
  console.log('\n📋 Package is ready for use in:');
  console.log('   - Web app API routes');
  console.log('   - Scan PWA API routes');
  console.log('   - Booking Proto API routes');
  
} catch (error) {
  console.error('❌ Failed to import package:', error.message);
  console.error('\n🔧 Troubleshooting steps:');
  console.error('   1. Run `yarn install` to ensure dependencies are linked');
  console.error('   2. Run `yarn workspace @event-services/netsuite-http build` to compile the package');
  console.error('   3. Check that the package.json exports are correct');
  process.exit(1);
}
