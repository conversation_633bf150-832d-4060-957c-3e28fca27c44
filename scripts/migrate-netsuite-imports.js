#!/usr/bin/env node

/**
 * <PERSON>ript to migrate NetSuite API imports from local lib to shared package
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

const API_DIR = path.join(__dirname, '../apps/web/pages/api');

// Find all JS files in the API directory
const apiFiles = glob.sync('**/*.js', { cwd: API_DIR });

console.log('🔄 Migrating NetSuite API imports to shared package...\n');

let migratedCount = 0;
let skippedCount = 0;

apiFiles.forEach(filePath => {
  const fullPath = path.join(API_DIR, filePath);
  const content = fs.readFileSync(fullPath, 'utf8');
  
  // Check if file uses the old import pattern
  const oldImportPattern = /import\s+{[^}]+}\s+from\s+["']\.\.\/\.\.\/\.\.\/lib\/netsuite-api["'];?/;
  const oldImportPattern2 = /import\s+{[^}]+}\s+from\s+["']\.\.\/\.\.\/\.\.\/\.\.\/lib\/netsuite-api["'];?/;
  const oldImportPattern3 = /import\s+{[^}]+}\s+from\s+["']\.\.\/\.\.\/lib\/netsuite-api["'];?/;
  
  if (oldImportPattern.test(content) || oldImportPattern2.test(content) || oldImportPattern3.test(content)) {
    console.log(`📝 Migrating: ${filePath}`);
    
    let newContent = content;
    
    // Replace import statements
    newContent = newContent.replace(
      /import\s+{([^}]+)}\s+from\s+["']\.\.\/\.\.\/\.\.\/lib\/netsuite-api["'];?/g,
      'import {\n$1,\n  addPerformanceHeaders\n} from "@event-services/netsuite-http";'
    );
    
    newContent = newContent.replace(
      /import\s+{([^}]+)}\s+from\s+["']\.\.\/\.\.\/\.\.\/\.\.\/lib\/netsuite-api["'];?/g,
      'import {\n$1,\n  addPerformanceHeaders\n} from "@event-services/netsuite-http";'
    );
    
    newContent = newContent.replace(
      /import\s+{([^}]+)}\s+from\s+["']\.\.\/\.\.\/lib\/netsuite-api["'];?/g,
      'import {\n$1,\n  addPerformanceHeaders\n} from "@event-services/netsuite-http";'
    );
    
    // Replace error handling calls
    newContent = newContent.replace(
      /handleApiError\(([^,]+),\s*([^,]+),\s*([^)]+)\)/g,
      'handleApiError($1, { res: $2, requestStart: $3 })'
    );
    
    // Replace performance headers
    newContent = newContent.replace(
      /res\.setHeader\("X-Response-Time",\s*`\$\{Date\.now\(\)\s*-\s*requestStart\}ms`\);\s*res\.setHeader\("X-NetSuite-Time",\s*`\$\{duration\}ms`\);/g,
      'addPerformanceHeaders(res, requestStart, duration);'
    );
    
    newContent = newContent.replace(
      /res\.setHeader\('X-Response-Time',\s*`\$\{Date\.now\(\)\s*-\s*requestStart\}ms`\);\s*res\.setHeader\('X-NetSuite-Time',\s*`\$\{duration\}ms`\);/g,
      'addPerformanceHeaders(res, requestStart, duration);'
    );
    
    // Write the updated content
    fs.writeFileSync(fullPath, newContent);
    migratedCount++;
  } else {
    skippedCount++;
  }
});

console.log(`\n✅ Migration complete!`);
console.log(`   Migrated: ${migratedCount} files`);
console.log(`   Skipped: ${skippedCount} files`);

if (migratedCount > 0) {
  console.log('\n📋 Next steps:');
  console.log('   1. Run yarn install to ensure dependencies are linked');
  console.log('   2. Test the migrated API endpoints');
  console.log('   3. Remove the old lib/netsuite-api.js file when all migrations are complete');
}
