{"name": "cs-event-services", "version": "12.5.81", "private": true, "workspaces": ["apps/*", "packages/*", "services/*"], "scripts": {"dev": "node scripts/dev.js", "dev:parallel": "turbo run dev --parallel --continue", "dev:web": "turbo run dev --filter=conventionsuite-web", "dev:scan": "turbo run dev --filter=conventionsuite-scan", "dev:booking": "turbo run dev --filter=booking-calendar", "dev:exhibitor": "cd apps/web && next dev", "dev-spa": "turbo run dev-spa --parallel", "build": "turbo run build", "build:web": "turbo run build --filter=conventionsuite-web", "build:scan": "turbo run build --filter=conventionsuite-scan", "build:booking": "turbo run build --filter=booking-calendar", "build-web": "turbo run build-web", "test": "turbo run test", "test:watch": "turbo run test:watch", "test:coverage": "turbo run test:coverage", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md}\"", "clean": "turbo run clean", "clean:node_modules": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +", "clean:build": "find . -name '.next' -type d -prune -exec rm -rf '{}' + && find . -name 'dist' -type d -prune -exec rm -rf '{}' +", "typecheck": "turbo run typecheck", "health": "node scripts/health-check.js", "ngrok": "cd packages/eslint-config/runConfigs && bash startNgrok.sh"}, "devDependencies": {"@oracle/suitecloud-unit-testing": "^1.6.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "chalk": "4.1.2", "concurrently": "8.2.2", "glob": "^11.0.3", "next-auth": "4.24.7", "prettier": "^3.2.5", "turbo": "^2.1.2"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4", "dependencies": {"date-fns": "^3.6.0", "swr": "^2.3.4"}}