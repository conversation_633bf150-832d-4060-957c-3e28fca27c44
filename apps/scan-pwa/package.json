{"name": "conventionsuite-scan", "version": "0.0.0", "license": "MIT", "scripts": {"dev": "next dev -p 4000", "dev:scan": "next dev -p 4000", "build-web": "next build", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "test": "echo 'No tests configured for scan-pwa'", "clean": "rm -rf .next && rm -rf dist"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@event-services/auth": "workspace:*", "@heroicons/react": "^2.1.3", "@mui/icons-material": "^6.1.2", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^6.1.2", "@mui/material-nextjs": "^6.1.2", "@mui/system": "^6.1.2", "@mui/x-data-grid": "^7.12.1", "@next/env": "^14.2.14", "@t3-oss/env-core": "^0.11.1", "@t3-oss/env-nextjs": "^0.11.1", "axios": "^1.7.4", "browser-image-compression": "^2.0.2", "cookies": "^0.9.1", "eslint-config-next": "latest", "formidable": "^3.5.2", "formik": "^2.4.6", "framer-motion": "^11.9.0", "html5-qrcode": "^2.3.8", "idb": "^8.0.1", "jiti": "^2.3.3", "next": "latest", "next-auth": "4.24.7", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "notistack": "^3.0.1", "pako": "^2.1.0", "react": "latest", "react-dom": "latest", "react-html-parser": "^2.0.2", "react-html5-camera-photo": "^1.5.11", "react-webcam": "^7.2.0", "request-promise-native": "^1.0.9", "simplebar-react": "^3.2.6", "swr": "^2.2.5", "yup": "^1.4.0", "zustand": "^4.5.4"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@types/cookies": "^0.9.0", "@types/formidable": "^3.4.5", "@types/node": "^20.8.4", "@types/react": "^18.2.28", "@types/request-promise-native": "^1.0.21", "autoprefixer": "^10.4.16", "eslint": "^9.2.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.5", "tailwindcss": "^3.3.3", "tailwindcss-safe-area": "^0.4.1", "typescript": "^5.2.2"}, "prettier": {"semi": false, "singleQuote": true, "jsxSingleQuote": true, "useTabs": true}, "packageManager": "yarn@3.6.4"}