import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import { makeNetSuiteRequest, buildNetSuiteUrl, handleApiError, setOptimizedHeaders } from "../../../../lib/netsuite-api";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const { method } = req;

    switch (method) {
      case "GET": {
        return res.status(200).json({
          ready: "Ready for address mutation.",
        });
      }

      case "POST": {
        // Validate required body
        if (!req.body) {
          return res.status(400).json({ error: "Request body is required" });
        }

        // Build NetSuite URL
        const url = buildNetSuiteUrl(
          "customscript_ng_cses_rl_get_user_info",
          "customdeploy_ng_cses_rl_get_user_info"
        );

        // Make request to NetSuite
        const { data, duration } = await makeNetSuiteRequest({
          url,
          method: "POST",
          body: req.body,
        });

        // Add performance headers
        res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
        res.setHeader("X-NetSuite-Time", `${duration}ms`);

        return res.status(201).json(data);
      }

      default:
        return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error) {
    return handleApiError(error, res, requestStart);
  }
}
