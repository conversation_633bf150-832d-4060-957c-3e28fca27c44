import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import { makeNetSuiteRequest, buildNetSuiteUrl, handleApiError } from "../../../lib/netsuite-api";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  // Only allow POST requests
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    // Validate required body
    if (!req.body) {
      return res.status(400).json({ error: "Request body is required" });
    }

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      "customscript_ng_rl_rcs_handle_tracking",
      "customdeploy_ng_rl_rcs_handle_tracking"
    );

    // Make request to NetSuite
    const { data, duration } = await makeNetSuiteRequest({
      url,
      method: "POST",
      body: req.body,
    });

    // Note: No cache headers set per user request
    
    // Add performance headers
    res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
    res.setHeader("X-NetSuite-Time", `${duration}ms`);

    return res.status(200).json(data);
  } catch (error) {
    return handleApiError(error, res, requestStart);
  }
}
