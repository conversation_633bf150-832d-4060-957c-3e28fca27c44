import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import { makeNetSuiteRequest, handleApiError } from "../../../lib/netsuite-api";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const { type, recid, transmitUrl } = req.query;
    
    // Validate required parameters
    if (!transmitUrl) {
      return res.status(400).json({ error: "Transmit URL is required" });
    }

    // Decode the transmit URL
    const decodedUrl = decodeURIComponent(transmitUrl);

    // Make request to NetSuite using the provided URL
    const { data, duration } = await makeNetSuiteRequest({
      url: decodedUrl,
      method: "GET",
    });

    // Add performance headers
    res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
    res.setHeader("X-NetSuite-Time", `${duration}ms`);

    return res.status(201).json(data);
  } catch (error) {
    return handleApiError(error, res, requestStart);
  }
}

// Disable body parsing for this API route as we don't need it
export const config = {
  api: {
    bodyParser: false,
  },
};
