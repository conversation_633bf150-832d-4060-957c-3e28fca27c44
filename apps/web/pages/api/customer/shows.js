import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import { makeNetSuiteRequest, buildNetSuiteUrl, handleApiError, setOptimizedHeaders } from "../../../lib/netsuite-api";

export default async function handler(req, res) {
  const requestStart = Date.now();
  
  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const customer = req.cookies.customer;
    
    // Validate required parameters
    if (!customer) {
      return res.status(400).json({ error: "Customer ID is required" });
    }

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      "customscript_rcs_rl_user_related_shows",
      "customdeploy_rcs_rl_user_related_shows",
      { id: customer }
    );

    // Make request to NetSuite
    const { data, duration } = await makeNetSuiteRequest({
      url,
      method: "GET",
    });

    // Set optimized response headers
    setOptimizedHeaders(res);
    
    // Add performance headers
    res.setHeader("X-Response-Time", `${Date.now() - requestStart}ms`);
    res.setHeader("X-NetSuite-Time", `${duration}ms`);

    return res.status(200).json(data);
  } catch (error) {
    return handleApiError(error, res, requestStart);
  }
}

// Disable body parsing for this API route as we don't need it
export const config = {
  api: {
    bodyParser: false,
  },
};
