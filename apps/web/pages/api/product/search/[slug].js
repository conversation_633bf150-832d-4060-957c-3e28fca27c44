import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";
import {
  makeNetSuiteRequest,
  buildNetSuiteUrl,
  handleApiError,
  setOptimizedHeaders,
  addPerformanceHeaders
} from "@event-services/netsuite-http";

export default async function itemSearchHandler(req, res) {
  const requestStart = Date.now();

  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Check authentication first to fail fast
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({
        error: "You must be signed in to view the protected content on this page.",
      });
    }

    const { slug, event, page, limit, sortBy } = req.query;

    // Validate required parameters
    if (!slug || !event) {
      return res.status(400).json({
        error: "Search term and event ID are required"
      });
    }

    // Build NetSuite URL
    const url = buildNetSuiteUrl(
      "customscript_ng_es_rl_search_item_paged",
      "customdeploy_ng_es_rl_search_item_paged",
      {
        search: slug,
        event,
        page,
        limit,
        sortBy
      }
    );

    try {
      const response = await request({
        uri: currRequest.url,
        method: "GET",
        oauth: {
          consumer_key: process.env.OAUTH1_CONSUMER_KEY,
          consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
          token: process.env.OAUTH1_ACCESS_TOKEN,
          token_secret: process.env.OAUTH1_TOKEN_SECRET,
          signature_method: "HMAC-SHA256",
          realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
          version: "1.0",
        },
        resolveWithFullResponse: true,
        json: true,
      });

      const data = response.body;
      console.debug('RES: " ', response);
      console.debug('Body: " ', data);
      res.json(data);
    } catch (err) {
      res.status(500).send({ 
        error: `Error processing item search request: ${err}` 
      });
    }
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
    // res.redirect('/auth/login')
  }
}
