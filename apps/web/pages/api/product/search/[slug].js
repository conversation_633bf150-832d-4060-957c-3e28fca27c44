import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

export default async function itemSearchHandler(req, res) {
  const session = await getServerSession(req, res, authOptions);
  // TODO: Write a simple request to pull a netSuite user via id
  const { slug, event, page, limit, sortBy } = req.query;

  if (session) {
    const currRequest = {
      url: `https://${
        process.env.NEXT_PUBLIC_ACCOUNT_URL_ID
      }.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_es_rl_search_item_paged&deploy=customdeploy_ng_es_rl_search_item_paged&search=${encodeURIComponent(
        slug,
      )}&event=${event}&page=${page}&limit=${limit}&sortBy=${sortBy}`,
      method: "GET",
    };

    try {
      const response = await request({
        uri: currRequest.url,
        method: "GET",
        oauth: {
          consumer_key: process.env.OAUTH1_CONSUMER_KEY,
          consumer_secret: process.env.OAUTH1_CONSUMER_SECRET,
          token: process.env.OAUTH1_ACCESS_TOKEN,
          token_secret: process.env.OAUTH1_TOKEN_SECRET,
          signature_method: "HMAC-SHA256",
          realm: process.env.NEXT_PUBLIC_ACCOUNT_ID,
          version: "1.0",
        },
        resolveWithFullResponse: true,
        json: true,
      });

      const data = response.body;
      console.debug('RES: " ', response);
      console.debug('Body: " ', data);
      res.json(data);
    } catch (err) {
      res.status(500).send({ 
        error: `Error processing item search request: ${err}` 
      });
    }
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
    // res.redirect('/auth/login')
  }
}
