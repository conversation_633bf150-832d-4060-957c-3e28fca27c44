{"name": "conventionsuite-web", "version": "5.0.0", "private": true, "scripts": {"dev": "next dev", "dev-zones": "next dev", "build": "next build", "build-web": "npx oxlint && next build", "export": "next build && next export", "build:local": "cross-env NODE_OPTIONS=--max-old-space-size=4096 next build", "start": "next start", "size": "node --max_old_space_size=4096", "fix:prettier": "prettier --write . '!.next' '!.node_modules'", "lint": "npx oxlint && next lint", "partytown": "partytown copylib public/~partytown", "check-migration": "node scripts/verify-api-migration.js"}, "lint-staged": {"**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,astro,svelte}": "ox<PERSON>", "*.+(js|jsx)": ["prettier --write --ignore-unknown"], "*.+(json|css|md)": ["prettier --write"]}, "dependencies": {"@emotion/cache": "^11.13.1", "@emotion/react": "^11.13.3", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.13.0", "@event-services/auth": "workspace:*", "@headlessui/react": "^2.1.9", "@heroicons/react": "^2.1.5", "@iconify/react": "^5.0.2", "@mui/icons-material": "^6.1.2", "@mui/lab": "^6.0.0-beta.10", "@mui/material": "^6.1.2", "@mui/material-nextjs": "^6.1.2", "@mui/styles": "6.1.2", "@mui/system": "^6.1.2", "@mui/x-data-grid-pro": "^7.19.0", "@mui/x-date-pickers": "^7.19.0", "@mui/x-date-pickers-pro": "^7.19.0", "@mui/x-license-pro": "^6.10.2", "@mui/x-tree-view-pro": "^7.22.1", "@next/bundle-analyzer": "15.4.1", "@next/third-parties": "^15.4.1", "@vercel/analytics": "^1.4.1", "apexcharts": "^3.49.0", "axios": "^1.10.0", "axios-mock-adapter": "^1.22.0", "clsx": "^2.1.1", "cookies": "^0.9.1", "cross-env": "^7.0.3", "currency.js": "^2.0.4", "date-fns": "*", "delay": "^6.0.0", "eslint-config-next": "15.4.1", "form-data": "^4.0.0", "formidable": "^3.5.1", "formik": "^2.4.6", "framer-motion": "^11.1.9", "google-map-react": "^2.2.1", "i18next": "^23.11.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "motion": "^12.20.5", "next": "15.4.1", "next-auth": "^4.24.11", "next-compose-plugins": "^2.2.1", "next-fonts": "^1.5.1", "next-i18next": "^15.3.0", "next-images": "^1.8.5", "notistack": "^3.0.1", "nprogress": "^0.2.0", "oauth-1.0a": "^2.2.6", "oauth-sign": "^0.9.0", "p-retry": "^6.2.0", "pure-react-carousel": "^1.30.1", "react": "19.1.0", "react-apexcharts": "^1.4.1", "react-dom": "19.1.0", "react-dropzone": "^14.2.3", "react-html-parser": "^2.0.2", "react-i18next": "^14.1.1", "react-imask": "^7.6.0", "react-lottie": "^1.2.10", "react-number-format": "^5.3.4", "react-scroll": "^1.9.0", "react-syntax-highlighter": "^15.6.1", "request": "^2.88.2", "request-promise-native": "^1.0.9", "simplebar-react": "^3.2.5", "stream": "^0.0.2", "stylis": "^4.3.2", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.5", "swr-devtools": "^1.3.2", "tinycolor2": "^1.6.0", "typewriter-effect": "^2.21.0", "uuid": "^9.0.1", "yup": "^1.4.0", "zustand": "^4.5.2"}, "devDependencies": {"@babel/eslint-parser": "^7.24.5", "@builder.io/partytown": "^0.10.2", "@next/eslint-plugin-next": "15.4.1", "@repo/eslint-config": "workspace:*", "@svgr/webpack": "^8.1.0", "@types/node": "^20.12.10", "@types/nprogress": "^0.2.3", "@types/react": "19.1.8", "@types/react-scroll": "^1.8.10", "@types/stylis": "^4.2.6", "autoprefixer": "^10.4.19", "babel-plugin-import": "^1.13.8", "eslint": "^9.2.0", "fetch-mock": "^9.11.0", "glob": "^11.0.0", "gzip-size": "6.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mkdirp": "^3.0.1", "ncp": "^2.0.0", "npm-only-allow": "^1.2.6", "patch-package": "^8.0.0", "postcss": "^8.4.38", "postinstall-postinstall": "^2.1.0", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "^0.5.7", "replace-in-file": "^8.2.0", "semantic-release": "^22.0.8", "storybook": "^7.5.3", "tailwindcss": "^3.4.3", "typescript": "^5.4.5"}, "overrides": {"@types/react": "19.1.8"}}