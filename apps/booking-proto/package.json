{"name": "booking-calendar", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env FORCE_COLOR=1 next dev -p 4001", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "prettier": "prettier --check \"**/*.{js,jsx,ts,tsx}\"", "prettier:fix": "prettier --write \"**/*.{js,jsx,ts,tsx}\"", "analyze": "cross-env ANALYZE=true yarn build", "storybook": "cross-env FORCE_COLOR=1 storybook dev -p 6006", "test-storybook": "cross-env FORCE_COLOR=1 test-storybook", "build-storybook": "cross-env FORCE_COLOR=1 storybook build", "test": "cross-env FORCE_COLOR=1 jest --passWithNoTests", "test:watch": "cross-env FORCE_COLOR=1 jest --watch", "test:coverage": "cross-env FORCE_COLOR=1 jest --coverage", "e2e:headless": "playwright test", "e2e:ui": "playwright test --ui", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "clean": "rm -rf .next && rm -rf dist && rm -rf coverage", "preinstall": "npx npm-only-allow@latest --PM yarn", "postinstall": "npx patch-package -y", "coupling-graph": "npx madge --extensions js,jsx,ts,tsx,css,md,mdx ./ --exclude '.next|tailwind.config.js|reset.d.ts|prettier.config.js|postcss.config.js|playwright.config.ts|next.config.js|next-env.d.ts|instrumentation.ts|e2e/|README.md|.storybook/|.eslintrc.js' --image graph.svg"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@event-services/auth": "workspace:^", "@fullcalendar/core": "6.1.18", "@fullcalendar/daygrid": "6.1.18", "@fullcalendar/interaction": "6.1.18", "@fullcalendar/list": "6.1.18", "@fullcalendar/multimonth": "6.1.18", "@fullcalendar/react": "6.1.18", "@fullcalendar/resource": "6.1.18", "@fullcalendar/resource-timeline": "^6.1.18", "@fullcalendar/timegrid": "6.1.18", "@hookform/resolvers": "^3.9.1", "@next/bundle-analyzer": "^14.2.3", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "latest", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "12.0.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.0.5", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^13.0.0", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@untitled-ui/icons-react": "^0.1.2", "@vercel/otel": "^1.8.3", "apexcharts": "^3.49.0", "autoprefixer": "^10.4.20", "axios": "^1.6.8", "axios-mock-adapter": "^1.22.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cookies": "^0.9.1", "cross-env": "^7.0.3", "date-fns": "latest", "embla-carousel-react": "8.5.1", "framer-motion": "^11.5.5", "immer": "latest", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "motion": "^12.6.3", "next": "15.2.4", "next-auth": "4.24.7", "next-compose-plugins": "^2.2.1", "next-themes": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "request-promise-native": "^1.0.9", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sync-external-store": "latest", "uuid": "latest", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "latest"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/plugin-syntax-flow": "^7.23.3", "@babel/plugin-transform-optional-chaining": "^7.23.4", "@babel/plugin-transform-react-jsx": "^7.23.4", "@jest/globals": "^29.7.0", "@opentelemetry/api": "1.7.0", "@opentelemetry/resources": "1.18.1", "@opentelemetry/sdk-node": "0.45.1", "@opentelemetry/sdk-trace-node": "1.18.1", "@opentelemetry/semantic-conventions": "1.18.1", "@playwright/test": "^1.40.0", "@storybook/addon-essentials": "^7.5.3", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "^7.5.3", "@storybook/blocks": "^7.5.3", "@storybook/nextjs": "^7.5.3", "@storybook/react": "^7.5.3", "@storybook/test-runner": "^0.15.2", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@total-typescript/ts-reset": "^0.5.1", "@types/jest": "^29.5.10", "@types/node": "^20.12.13", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/request-promise-native": "^1.0.21", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "all-contributors-cli": "^6.26.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "8.54.0", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-tailwindcss": "^3.13.0", "fetch-mock": "^9.11.0", "glob": "^11.0.0", "gzip-size": "6.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mkdirp": "^3.0.1", "ncp": "^2.0.0", "npm-only-allow": "^1.2.6", "patch-package": "^8.0.0", "postcss": "^8.4.31", "postinstall-postinstall": "^2.1.0", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "^0.5.7", "replace-in-file": "^8.2.0", "semantic-release": "^22.0.8", "storybook": "^7.5.3", "tailwindcss": "^3.3.5", "ts-jest": "^29.1.1", "tsc": "^2.0.4", "typescript": "5.4.5", "webpack": "5.89.0"}, "engines": {"node": ">=20.0.0"}, "packageManager": "yarn@1.22.19"}