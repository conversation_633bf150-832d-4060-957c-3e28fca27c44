import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import { ThemeProvider } from "src/components/theme-provider"
import { ToastManager } from "src/components/toast-manager"
import { Providers } from "src/components/providers"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Booking Calendar",
  description: "A booking calendar for venue operations",
    generator: 'v0.dev'
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
            {children}
            <ToastManager />
          </ThemeProvider>
        </Providers>
      </body>
    </html>
  )
}



import './globals.css'