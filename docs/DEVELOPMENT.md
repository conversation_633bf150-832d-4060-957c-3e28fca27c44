# CS Event Services - Development Guide

This guide covers the development workflow for the CS Event Services monorepo, which includes three main applications: Web App, Scan PWA, and Booking Proto.

## 🚀 Quick Start

### Prerequisites

- Node.js >= 18
- Yarn >= 3.6.4
- Git

### Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/newgen-business-solutions/cs-event-services.git
   cd cs-event-services
   ```

2. **Install dependencies**
   ```bash
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your actual values
   ```

4. **Start all development servers**
   ```bash
   yarn dev
   ```

This will start all three applications in the correct order:
- **Web App**: http://localhost:3000 (Main application)
- **Scan <PERSON>**: http://localhost:4000 (Accessible via /scan route)
- **Booking Proto**: http://localhost:4001 (Accessible via /booking route)

## 📁 Project Structure

```
cs-event-services/
├── apps/
│   ├── web/                 # Main web application (Port 3000)
│   ├── scan-pwa/           # Scan <PERSON>WA application (Port 4000)
│   └── booking-proto/      # Booking prototype (Port 4001)
├── packages/
│   ├── auth/               # Shared authentication package
│   ├── eslint-config/      # Shared ESLint configuration
│   ├── typescript-config/  # Shared TypeScript configuration
│   └── ui/                 # Shared UI components
├── scripts/
│   └── dev.js             # Development orchestration script
├── turbo.json             # Turborepo configuration
└── package.json           # Root package.json
```

## 🛠 Development Commands

### Root Level Commands

| Command | Description |
|---------|-------------|
| `yarn dev` | Start all development servers in order with orchestration |
| `yarn dev:parallel` | Start all development servers in parallel (faster but less organized) |
| `yarn dev:web` | Start only the web app |
| `yarn dev:scan` | Start only the scan PWA |
| `yarn dev:booking` | Start only the booking proto |
| `yarn build` | Build all applications |
| `yarn test` | Run tests across all packages |
| `yarn lint` | Lint all packages |
| `yarn lint:fix` | Auto-fix linting issues |
| `yarn typecheck` | Type check all TypeScript files |
| `yarn clean` | Clean all build artifacts |

### Individual App Commands

Each app can be developed independently by navigating to its directory:

```bash
# Web App
cd apps/web
yarn dev

# Scan PWA  
cd apps/scan-pwa
yarn dev

# Booking Proto
cd apps/booking-proto
yarn dev
```

## 🔧 Configuration

### Port Allocation

- **Web App**: 3000
- **Scan PWA**: 4000
- **Booking Proto**: 4001
- **Storybook** (booking-proto): 6006

### Environment Variables

The monorepo uses shared environment variables across all applications. Key variables include:

- `OAUTH_CLIENT_ID` / `OAUTH_CLIENT_SECRET` - NetSuite OAuth credentials
- `NEXTAUTH_SECRET` - Shared secret for NextAuth
- `NEXT_PUBLIC_ACCOUNT_URL_ID` - NetSuite account identifier
- `NEXT_PUBLIC_SCAN_DOMAIN` / `NEXT_PUBLIC_BOOKING_DOMAIN` - Zone routing domains

### Zone Routing

The web app acts as the main entry point and routes requests to other applications:

- `/scan/*` → Scan PWA (localhost:4000)
- `/booking/*` → Booking Proto (localhost:4001)

## 🏗 Build System

The project uses Turborepo for efficient builds and caching:

### Build Pipeline

1. **Shared packages** are built first (`@event-services/auth`, etc.)
2. **Type checking** runs for all TypeScript files
3. **Applications** are built with optimized caching
4. **Outputs** are cached for faster subsequent builds

### Caching Strategy

- Build outputs are cached based on input file changes
- Environment variables are included in cache keys
- Shared dependencies trigger rebuilds of dependent packages

## 🧪 Testing

### Running Tests

```bash
# Run all tests
yarn test

# Run tests for specific app
yarn test --filter=booking-calendar

# Run tests in watch mode
yarn test:watch

# Generate coverage reports
yarn test:coverage
```

### Test Structure

- **Unit tests**: `*.test.ts` / `*.test.tsx`
- **Integration tests**: `*.spec.ts` / `*.spec.tsx`
- **E2E tests**: Located in `e2e/` directories

## 🔍 Debugging

### Development Server Logs

The orchestration script provides colored, timestamped logs for each service:

```
[10:30:15] [Web App] ✓ ready - started server on 0.0.0.0:3000
[10:30:17] [Scan PWA] ✓ ready - started server on 0.0.0.0:4000  
[10:30:19] [Booking Proto] ✓ ready - started server on 0.0.0.0:4001
```

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 4000, and 4001 are available
2. **Environment variables**: Check that all required env vars are set
3. **Build failures**: Run `yarn clean` and rebuild
4. **Auth issues**: Verify NetSuite OAuth credentials

## 📦 Package Management

### Adding Dependencies

```bash
# Add to root workspace
yarn add package-name

# Add to specific app
yarn workspace conventionsuite-web add package-name

# Add to shared package
yarn workspace @event-services/auth add package-name
```

### Workspace Dependencies

Use workspace protocol for internal dependencies:

```json
{
  "dependencies": {
    "@event-services/auth": "workspace:^"
  }
}
```

## 🚢 Deployment

### Building for Production

```bash
# Build all applications
yarn build

# Build specific application
yarn build:web
yarn build:scan  
yarn build:booking
```

### Environment-Specific Builds

Each application can be built with environment-specific configurations by setting appropriate environment variables before building.

## 🤝 Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Run `yarn lint:fix` and `yarn typecheck`
4. Test your changes with `yarn test`
5. Create a pull request

### Code Quality

- ESLint and Prettier are configured for consistent code style
- TypeScript strict mode is enabled
- Pre-commit hooks ensure code quality

## 📚 Additional Resources

- [Turborepo Documentation](https://turbo.build/repo/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [NextAuth.js Documentation](https://next-auth.js.org/)
- [NetSuite OAuth Documentation](https://docs.oracle.com/en/cloud/saas/netsuite/ns-online-help/section_161942238213.html)
