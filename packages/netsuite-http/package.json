{"name": "@event-services/netsuite-http", "version": "1.0.0", "description": "Shared NetSuite HTTP client with OAuth, retry logic, and performance optimizations", "main": "./index.js", "types": "./index.d.ts", "exports": {".": {"import": "./index.js", "require": "./index.js", "types": "./index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["netsuite", "http", "o<PERSON>h", "api", "event-services"], "author": "Newgen Business Solutions", "dependencies": {"axios": "^1.7.4", "oauth-1.0a": "^2.2.6", "p-retry": "^6.2.0"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.4.5"}, "repository": {"type": "git", "url": "https://github.com/newgen-business-solutions/cs-event-services.git"}, "license": "MIT"}